import asyncio
import sys
import os
import glob
import json
import uuid
from typing import Optional

from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn

from langchain_core.messages import HumanMessage, AIMessage, ToolMessage # 确保导入 ToolMessage 以便检查类型
from langchain.memory import ChatMessageHistory
from langchain_ollama import ChatOllama
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent

# --- 1. 配置与 FastAPI 应用初始化 ---

app = FastAPI(
    title="流式对话 Agent API",
    description="一个只提供带上下文的流式对话接口的 Agent API。",
    version="3.1.0" # 版本升级
)

# 用于 API 请求的 Pydantic 模型
class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None

class CreateConversationRequest(BaseModel):
    title: Optional[str] = None

class DeleteConversationRequest(BaseModel):
    conversation_id: str

class ConversationResponse(BaseModel):
    conversation_id: str
    title: Optional[str] = None
    created_at: str
    message: str

# --- 2. 核心服务组件 ---

# 用于管理对话历史的全局字典 (生产环境请用 Redis 或数据库替代)
conversation_histories = {}
# 用于存储对话元数据的字典
conversation_metadata = {}

# 全局变量
llm = None
agent = None

def discover_tools():
    """自动发现 ./tools 目录下的所有工具服务。"""
    tools_config = {}
    tools_dir = "./tools"
    if not os.path.exists(tools_dir):
        print(f"警告：'{tools_dir}' 目录不存在")
        return tools_config
    server_files = glob.glob(os.path.join(tools_dir, "*_server.py"))
    for server_file in server_files:
        filename = os.path.basename(server_file)
        tool_name = filename.replace("_server.py", "")
        tools_config[tool_name] = {
            "command": sys.executable,
            "args": [server_file],
            "transport": "stdio",
        }
        print(f"发现工具: {tool_name} -> {server_file}")
    return tools_config

def get_or_create_history(conversation_id: Optional[str]) -> tuple[str, ChatMessageHistory]:
    """根据 ID 获取或创建新的对话历史。"""
    if conversation_id is None or conversation_id not in conversation_histories:
        conversation_id = str(uuid.uuid4())
        conversation_histories[conversation_id] = ChatMessageHistory()
        print(f"创建新的对话，ID: {conversation_id}")
    return conversation_id, conversation_histories[conversation_id]

def create_new_conversation(title: Optional[str] = None) -> tuple[str, str]:
    """创建新的对话。"""
    from datetime import datetime
    conversation_id = str(uuid.uuid4())
    created_at = datetime.now().isoformat()

    # 创建对话历史
    conversation_histories[conversation_id] = ChatMessageHistory()

    # 存储对话元数据
    conversation_metadata[conversation_id] = {
        "title": title or f"对话 {conversation_id[:8]}",
        "created_at": created_at
    }

    print(f"创建新对话，ID: {conversation_id}, 标题: {conversation_metadata[conversation_id]['title']}")
    return conversation_id, created_at

def delete_conversation(conversation_id: str) -> bool:
    """删除指定的对话。"""
    if conversation_id in conversation_histories:
        del conversation_histories[conversation_id]
        if conversation_id in conversation_metadata:
            del conversation_metadata[conversation_id]
        print(f"删除对话，ID: {conversation_id}")
        return True
    return False

# --- 3. FastAPI 生命周期与接口 ---

@app.on_event("startup")
async def startup_event():
    """应用启动时，初始化 LLM 和 Agent。"""
    global llm, agent
    print("--- 服务器启动中 ---")
    print("正在初始化 Ollama 模型...")
    llm = ChatOllama(base_url="http://localhost:11444", model="qwen3:32b")
    print(f"LLM 已加载: {llm.model}")
    print("正在自动发现并初始化工具...")
    tools_config = discover_tools()
    client = MultiServerMCPClient(tools_config)
    try:
        tools = await client.get_tools()
        agent = create_react_agent(llm, tools)
        print("--- Agent 已就绪，API 服务已上线 ---")
    except Exception as e:
        print(f"发生初始化错误 ({type(e).__name__})：{e}")
        agent = None

@app.post("/conversations", response_model=ConversationResponse)
async def create_conversation(request: CreateConversationRequest):
    """
    创建新的对话
    """
    try:
        conversation_id, created_at = create_new_conversation(request.title)

        return ConversationResponse(
            conversation_id=conversation_id,
            title=conversation_metadata[conversation_id]["title"],
            created_at=created_at,
            message="对话创建成功"
        )
    except Exception as e:
        print(f"创建对话时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}")

@app.delete("/conversations/{conversation_id}")
async def delete_conversation_endpoint(conversation_id: str):
    """
    删除指定的对话
    """
    try:
        success = delete_conversation(conversation_id)
        if success:
            return {"message": "对话删除成功", "conversation_id": conversation_id}
        else:
            raise HTTPException(status_code=404, detail="对话不存在")
    except HTTPException:
        raise
    except Exception as e:
        print(f"删除对话时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")

@app.get("/conversations")
async def list_conversations():
    """
    获取所有对话列表
    """
    try:
        conversations = []
        for conv_id, metadata in conversation_metadata.items():
            conversations.append({
                "conversation_id": conv_id,
                "title": metadata["title"],
                "created_at": metadata["created_at"]
            })

        return {
            "conversations": conversations,
            "total": len(conversations)
        }
    except Exception as e:
        print(f"获取对话列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话列表失败: {str(e)}")

@app.post("/chat")
async def stream_chat(request: ChatRequest):
    """
    唯一的对话接口，使用 Server-Sent Events (SSE) 进行流式响应并维护上下文。
    """
    if not agent:
        raise HTTPException(status_code=503, detail="Agent 服务不可用。")

    conv_id, history = get_or_create_history(request.conversation_id)
    print(f"\n收到流式请求 (ID: {conv_id}): {request.message}")

    input_messages = history.messages + [HumanMessage(content=request.message)]

    async def event_stream():
        """事件流生成器。"""
        final_answer_chunks = []
        current_tool_name = None
        current_tool_input = None
        tool_result = None
        is_after_tool = False

        try:
            async for event in agent.astream_events({"messages": input_messages}, version="v1"):
                kind = event["event"]

                if kind == "on_chat_model_stream":
                    content = event["data"]["chunk"].content
                    if content:
                        final_answer_chunks.append(content)
                        yield f"data: {json.dumps({'type': 'token', 'content': content})}\n\n"

                elif kind == "on_tool_start":
                    current_tool_name = event["name"]
                    current_tool_input = event["data"].get("input")
                    is_after_tool = False

                    # 根据工具名称生成对应的获取结果描述
                    tool_descriptions = {
                        "check_new_mail": "获取邮件结果",
                        "get_pending_items": "获取s6000结果",
                        "query_tianyan_alerts": "获取天眼结果",
                        "get_all_scan_tasks": "获取漏扫结果",
                        "get_task_vulnerabilities": "获取漏扫结果"
                    }

                    tool_desc = tool_descriptions.get(current_tool_name, f"获取{current_tool_name}结果")

                    data = {
                        "type": "tool_start",
                        "tool": current_tool_name,
                        "input": current_tool_input,
                        "description": tool_desc
                    }
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

                elif kind == "on_tool_end":
                    # 从原始输出中提取可序列化的内容
                    raw_output = event["data"].get("output")
                    serializable_output = raw_output
                    if isinstance(raw_output, ToolMessage):
                        serializable_output = raw_output.content

                    # 保存工具结果，稍后在大模型回答后输出
                    tool_result = {
                        "tool_name": current_tool_name,
                        "raw_output": serializable_output
                    }
                    is_after_tool = True

                    data = {
                        "type": "tool_end",
                        "tool": current_tool_name,
                        "output": serializable_output
                    }
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

            # 在所有流式输出结束后，输出工具调用的原始返回
            if tool_result and is_after_tool:
                # 根据工具名称生成对应的获取结果描述
                tool_descriptions = {
                    "check_new_mail": "邮件",
                    "get_pending_items": "s6000",
                    "query_tianyan_alerts": "天眼",
                    "get_all_scan_tasks": "漏扫",
                    "get_task_vulnerabilities": "漏扫"
                }

                tool_type = tool_descriptions.get(tool_result["tool_name"], tool_result["tool_name"])
                success_msg = f"成功获取{tool_type}结果"

                # 输出成功获取结果的消息
                yield f"data: {json.dumps({'type': 'tool_success', 'content': success_msg})}\n\n"

                # 输出工具调用的原始返回（不截断）
                yield f"data: {json.dumps({'type': 'tool_raw_output', 'content': tool_result['raw_output']})}\n\n"

            final_answer = "".join(final_answer_chunks)
            if final_answer: # 只有在有实际回答时才更新历史
                history.add_user_message(request.message)
                history.add_ai_message(final_answer)

            yield f"data: {json.dumps({'type': 'end', 'conversation_id': conv_id})}\n\n"
        except Exception as e:
            print(f"流式响应过程中发生错误: {e}")
            yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

    return StreamingResponse(event_stream(), media_type="text/event-stream")

# --- 4. 运行服务器 ---
if __name__ == "__main__":
    print("启动 Uvicorn 服务器，访问 http://0.0.0.0:28123")
    uvicorn.run(app, host="0.0.0.0", port=28123)