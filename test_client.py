import requests
import json
import time

# API 服务器的地址
API_URL = "http://127.0.0.1:28123/chat"

# 要按顺序提出的问题列表
CONVERSATION_STEPS = [
    "检查一下有没有新邮件",
    "显示当前的漏洞扫描任务状态",
    "查看S60000系统中的工作通知",
    "建议如何处理这些工作通知？"
]

def run_test():
    """
    运行一个自动化的、带上下文的对话测试。
    """
    conversation_id = None  # 初始化 conversation_id，用于维护对话上下文

    for i, question in enumerate(CONVERSATION_STEPS):
        print("="*50)
        print(f"第 {i+1} 轮对话")
        print(f"--- 正在提问 ---> [User]: {question}")
        print("--- 等待响应 ---> [Agent]: ", end="", flush=True)

        payload = {
            "message": question,
            "conversation_id": conversation_id  # 第一次为 None，后续为服务器返回的 ID
        }

        try:
            # 使用 stream=True 来接收流式响应
            with requests.post(API_URL, json=payload, stream=True) as response:
                if response.status_code != 200:
                    print(f"\n错误：服务器返回状态码 {response.status_code}")
                    print(response.text)
                    break
                
                # 逐行处理服务器发送的 SSE 事件
                for line in response.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        if decoded_line.startswith('data:'):
                            try:
                                data_str = decoded_line[5:].strip()
                                data = json.loads(data_str)
                                
                                # 根据事件类型处理
                                if data['type'] == 'thinking':
                                    # 大模型思考过程
                                    print(data['content'], end="", flush=True)
                                elif data['type'] == 'thinking_summary':
                                    # 思考过程总结
                                    print(f"\n\t[思考总结]: {data['content']}", flush=True)
                                elif data['type'] == 'tool_start':
                                    # 工具调用开始，显示描述和工具信息
                                    tool_desc = data.get('description', '工具调用')
                                    print(f"\n\t[{tool_desc}]: 正在使用工具 '{data['tool']}'", flush=True)
                                    if data.get('input'):
                                        print(f"\t[工具参数]: {json.dumps(data['input'], ensure_ascii=False)}", flush=True)
                                elif data['type'] == 'tool_end':
                                    # 工具调用结束，显示原始返回
                                    print(f"\t[工具调用完成]: '{data['tool']}'", flush=True)
                                    if data.get('raw_output'):
                                        # 格式化显示原始返回，限制长度避免输出过长
                                        raw_output_str = json.dumps(data['raw_output'], ensure_ascii=False, indent=2)
                                        if len(raw_output_str) > 500:
                                            raw_output_str = raw_output_str[:500] + "...[截断]"
                                        print(f"\t[原始返回]: {raw_output_str}", flush=True)
                                    print("\t[Agent]: ", end="", flush=True) # 提示 Agent 继续思考
                                elif data['type'] == 'token':
                                    # 最终回答的token
                                    print(data['content'], end="", flush=True)
                                elif data['type'] == 'end':
                                    # 对话结束，保存 conversation_id 以用于下一次请求
                                    conversation_id = data['conversation_id']
                                    print("\n--- 本轮对话结束 ---")
                                elif data['type'] == 'error':
                                    print(f"\n服务器端发生错误: {data['content']}")

                            except json.JSONDecodeError:
                                print(f"\n无法解析的 JSON 数据: {data_str}")
            
            # 在两轮对话之间稍作停顿
            time.sleep(2)

        except requests.exceptions.RequestException as e:
            print(f"\n请求失败，请检查服务器是否正在运行: {e}")
            break

if __name__ == "__main__":
    run_test()