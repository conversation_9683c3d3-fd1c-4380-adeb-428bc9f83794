import requests
import json
import time

# API 服务器的地址
BASE_URL = "http://127.0.0.1:28123"
CHAT_URL = f"{BASE_URL}/chat"
CONVERSATIONS_URL = f"{BASE_URL}/conversations"

# 要按顺序提出的问题列表
CONVERSATION_STEPS = [
    "检查一下有没有新邮件",
    "显示当前的漏洞扫描任务状态",
    "查看S60000系统中的工作通知",
    "建议如何处理这些工作通知？"
]

def test_create_conversation(title=None):
    """测试创建新对话接口"""
    print("=" * 50)
    print("测试: 创建新对话")
    print("-" * 30)

    data = {}
    if title:
        data["title"] = title

    try:
        response = requests.post(CONVERSATIONS_URL, json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 创建对话成功:")
            print(f"   对话ID: {result['conversation_id'][:]}...")
            print(f"   标题: {result['title']}")
            print(f"   创建时间: {result['created_at']}")
            return result['conversation_id']
        else:
            print(f"❌ 创建对话失败，状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 创建对话异常: {e}")
        return None

def test_list_conversations():
    """测试获取对话列表接口"""
    print("\n" + "=" * 50)
    print("测试: 获取对话列表")
    print("-" * 30)

    try:
        response = requests.get(CONVERSATIONS_URL)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取对话列表成功:")
            print(f"   总对话数: {result['total']}")
            for conv in result['conversations']:
                print(f"   - ID: {conv['conversation_id'][:8]}... 标题: {conv['title']}")
            return result['conversations']
        else:
            print(f"❌ 获取对话列表失败，状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return []
    except Exception as e:
        print(f"❌ 获取对话列表异常: {e}")
        return []

def test_delete_conversation(conversation_id):
    """测试删除对话接口"""
    print("\n" + "=" * 50)
    print("测试: 删除指定对话")
    print("-" * 30)

    if not conversation_id:
        print("❌ 无有效的对话ID，跳过删除测试")
        return False

    url = f"{CONVERSATIONS_URL}/{conversation_id}"

    try:
        response = requests.delete(url)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 删除对话成功:")
            print(f"   消息: {result['message']}")
            print(f"   对话ID: {result['conversation_id'][:8]}...")
            return True
        else:
            print(f"❌ 删除对话失败，状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 删除对话异常: {e}")
        return False

def run_chat_test(conversation_id=None):
    """
    运行一个自动化的、带上下文的对话测试。
    """
    print("\n" + "=" * 50)
    print("测试: 与大模型交互对话")
    print("-" * 30)

    for i, question in enumerate(CONVERSATION_STEPS):
        print("="*50)
        print(f"第 {i+1} 轮对话")
        print(f"--- 正在提问 ---> [User]: {question}")
        print("--- 等待响应 ---> [Agent]: ", end="", flush=True)

        payload = {
            "message": question,
            "conversation_id": conversation_id  # 使用传入的对话ID
        }

        try:
            # 使用 stream=True 来接收流式响应
            with requests.post(CHAT_URL, json=payload, stream=True) as response:
                if response.status_code != 200:
                    print(f"\n错误：服务器返回状态码 {response.status_code}")
                    print(response.text)
                    break

                # 逐行处理服务器发送的 SSE 事件
                for line in response.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        if decoded_line.startswith('data:'):
                            try:
                                data_str = decoded_line[5:].strip()
                                data = json.loads(data_str)

                                # 根据事件类型处理
                                if data['type'] == 'token':
                                    # 大模型思考过后的结果（流式输出）
                                    print(data['content'], end="", flush=True)
                                elif data['type'] == 'tool_start':
                                    # 工具调用开始（静默处理，不显示）
                                    pass
                                elif data['type'] == 'tool_end':
                                    # 工具调用结束（静默处理，不显示）
                                    pass
                                elif data['type'] == 'tool_success':
                                    # 成功获取xx结果
                                    print(f"\n\n{data['content']}", flush=True)
                                elif data['type'] == 'tool_raw_output':
                                    # 工具调用的原始返回（不截断）
                                    raw_output = data['content']
                                    if isinstance(raw_output, (dict, list)):
                                        formatted_output = json.dumps(raw_output, ensure_ascii=False, indent=2)
                                    else:
                                        formatted_output = str(raw_output)
                                    print(formatted_output, flush=True)
                                elif data['type'] == 'end':
                                    # 对话结束，保存 conversation_id 以用于下一次请求
                                    conversation_id = data['conversation_id']
                                    print("\n--- 本轮对话结束 ---")
                                elif data['type'] == 'error':
                                    print(f"\n服务器端发生错误: {data['content']}")

                            except json.JSONDecodeError:
                                print(f"\n无法解析的 JSON 数据: {data_str}")

            # 在两轮对话之间稍作停顿
            time.sleep(2)

        except requests.exceptions.RequestException as e:
            print(f"\n请求失败，请检查服务器是否正在运行: {e}")
            break

def run_full_test():
    """运行完整的API测试"""
    print("API接口完整测试工具")
    print("测试对话管理的所有接口")

    # 检查服务器是否可用
    try:
        response = requests.get(CONVERSATIONS_URL, timeout=5)
        print(f"✅ 服务器连接正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保服务器在 http://127.0.0.1:28123 上运行")
        return

    # 1. 测试创建新对话
    conversation_id = test_create_conversation("测试对话 - 完整流程")

    # 2. 显示当前对话列表
    test_list_conversations()

    # 3. 测试与大模型交互对话
    if conversation_id:
        run_chat_test(conversation_id)

    # 4. 再次显示对话列表
    test_list_conversations()

    # 5. 测试删除对话
    if conversation_id:
        test_delete_conversation(conversation_id)

    # 6. 最后显示对话列表
    test_list_conversations()

    print("\n" + "=" * 50)
    print("所有测试完成")

def run_simple_test():
    """运行简单的对话测试（原有功能）"""
    print("简单对话测试")
    print("使用默认对话流程")
    run_chat_test()

def main():
    """主函数 - 提供选择菜单"""
    print("=" * 60)
    print("MCP 对话服务器测试工具")
    print("=" * 60)
    print("完整API测试 (创建、对话、删除)")
    run_full_test()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")